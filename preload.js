const { contextBridge, ipc<PERSON>enderer } = require('electron')

/**
 * 安全的 IPC 通信桥接
 * 通过 contextBridge 暴露安全的 API 给渲染进程
 */
contextBridge.exposeInMainWorld('electronAPI', {
  // 订单相关操作
  orders: {
    // 获取所有订单
    getAll: (options) => ipcRenderer.invoke('get-all-orders', options),

    // 获取单个订单
    getById: (orderId) => ipcRenderer.invoke('get-order', orderId),

    // 搜索订单
    search: (criteria) => ipcRenderer.invoke('search-orders', criteria),

    // 添加订单
    add: (orderData) => ipcRenderer.invoke('add-order', orderData),

    // 更新订单
    update: (orderData) => ipcRenderer.invoke('update-order', orderData),

    // 删除订单
    delete: (orderId) => ipcRenderer.invoke('delete-order', orderId),

    // 获取统计信息
    getStatistics: (criteria) => ipcRenderer.invoke('get-order-statistics', criteria),

    // 获取今日统计
    getTodayStatistics: () => ipcRenderer.invoke('get-today-statistics'),

    // 搜索材料类型
    searchMaterialTypes: (materialName) => ipcRenderer.invoke('search-material-types', materialName),

    // 获取所有材料类型
    getAllMaterialTypes: () => ipcRenderer.invoke('get-all-material-types')
  },

  // 文件操作
  files: {
    // 导出 CSV
    exportCSV: (csvContent) => ipcRenderer.invoke('export-csv', csvContent)
  },

  // 应用信息
  app: {
    // 获取应用版本
    getVersion: () => ipcRenderer.invoke('get-app-version'),

    // 获取平台信息
    getPlatform: () => ipcRenderer.invoke('get-platform')
  },

  // 事件监听器
  on: (channel, callback) => {
    // 只允许特定的频道
    const validChannels = ['order-updated', 'order-deleted', 'order-added']
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback)
    }
  },

  // 移除事件监听器
  removeListener: (channel, callback) => {
    const validChannels = ['order-updated', 'order-deleted', 'order-added']
    if (validChannels.includes(channel)) {
      ipcRenderer.removeListener(channel, callback)
    }
  }
})

// 开发环境下的调试信息
if (process.env.NODE_ENV === 'development') {
  contextBridge.exposeInMainWorld('electronDebug', {
    log: (...args) => console.log('[Preload]', ...args),
    error: (...args) => console.error('[Preload]', ...args)
  })
}
